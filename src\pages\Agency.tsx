import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Header from '@/components/Header';
import CTAStrip from '@/components/CTAStrip';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Carousel, CarouselContent, CarouselItem } from '@/components/ui/carousel';
import { ExternalLink } from 'lucide-react';

const Agency = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  const services = [
    {
      title: "Artist Development",
      description: "Strategic career planning and creative direction to elevate our artists' profiles in the global electronic music scene.",
      action: "Develop Your Career"
    },
    {
      title: "Global Booking",
      description: "Worldwide tour management and festival bookings with our extensive network of premium venues and promoters.",
      action: "Book Worldwide"
    },
    {
      title: "Digital Strategy", 
      description: "Comprehensive digital marketing, social media management, and streaming optimization to maximize reach and engagement.",
      action: "Grow Digitally"
    }
  ];

  const clients = [
    "Tomorrowland", "Ultra Music Festival", "Coachella", "Electric Daisy Carnival", 
    "Creamfields", "Anjunabeats", "Spinnin' Records", "Armada Music",
    "Defected Records", "Monstercat", "OWSLA", "Mad Decent", "Beatport", "SoundCloud"
  ];

  const caseStudies = [
    {
      title: "Global Festival Circuit",
      description: "Secured headline slots at 15+ major festivals worldwide for emerging artist"
    },
    {
      title: "Digital Transformation", 
      description: "300% increase in streaming numbers through strategic digital campaign"
    },
    {
      title: "Brand Partnership",
      description: "Landmark collaboration with luxury fashion house for exclusive collection"
    }
  ];

  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 cosmic-grid opacity-30"></div>
      <div className="absolute top-[450px] left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full">
        <div className="w-full h-full opacity-10 bg-primary blur-[120px]"></div>
      </div>
      
      <div className="relative z-10 flex flex-col min-h-screen">
        <Header />
        <main className="flex-1">
          {/* Hero Section */}
          <section className="relative w-full py-20 md:py-28 px-6 md:px-12 flex flex-col items-center justify-center">
            <div className={`relative z-10 max-w-5xl text-center space-y-8 transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
              <div className="flex justify-center">
                <span className="inline-flex items-center gap-2 px-3 py-1 text-xs font-medium rounded-full bg-muted text-primary">
                  <span className="flex h-2 w-2 rounded-full bg-primary"></span>
                  Leading Artist Management
                </span>
              </div>
              
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-semibold text-balance text-foreground">
                Shaping the future of <span className="text-primary">artist management</span>
              </h1>
              
              <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                We elevate exceptional artists to global recognition through strategic management, innovative partnerships, and unparalleled industry expertise.
              </p>
            </div>
          </section>

        {/* About Section */}
        <section className="py-16 px-6 md:px-12">
          <div className="max-w-4xl mx-auto text-center space-y-6">
            <h2 className="text-3xl md:text-4xl font-semibold">Our mission</h2>
            <p className="text-lg text-muted-foreground leading-relaxed text-balance">
              For over a decade, we've been at the forefront of electronic music, discovering and nurturing talent that defines the sound of tomorrow. Our boutique approach ensures every artist receives personalized attention while leveraging our extensive global network to create extraordinary opportunities.
            </p>
            <div className="flex justify-center pt-2">
              <Link to="/agency">
                <button className="group inline-flex items-center gap-2 text-lg md:text-xl text-foreground hover:text-foreground transition-all duration-200 cursor-pointer">
                  <span>More about us</span>
                  <svg 
                    className="w-5 h-5 transition-transform duration-200 group-hover:translate-x-1" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </Link>
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 px-6 md:px-12">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <Card key={index} className="border-border bg-card hover:border-primary/20 transition-all duration-300">
                  <CardContent className="p-8 space-y-6 text-left">
                    <h3 className="text-2xl font-semibold">{service.title}</h3>
                    <p className="text-muted-foreground leading-relaxed">{service.description}</p>
                    <button className="group inline-flex items-center gap-2 text-lg text-foreground hover:text-primary transition-all duration-200 cursor-pointer">
                      <span>{service.action}</span>
                      <svg 
                        className="w-5 h-5 transition-transform duration-200 group-hover:translate-x-1" 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Clients Carousel - Seamless Integration */}
        <section className="py-8 overflow-hidden border-y border-border bg-muted">
          <Carousel
            opts={{
              align: "start",
              loop: true,
              skipSnaps: false,
              dragFree: true
            }}
            className="w-full"
          >
            <CarouselContent className="flex">
              {[...clients, ...clients].map((client, index) => (
                <CarouselItem key={index} className="basis-auto">
                  <div className="flex items-center justify-center px-8 py-4">
                    <span className="text-muted-foreground font-medium whitespace-nowrap">{client}</span>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </section>

        {/* Case Studies */}
        <section className="py-20 px-6 md:px-12">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-semibold mb-4">Success Stories</h2>
              <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                Real results from our strategic partnerships and innovative campaigns
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {caseStudies.map((study, index) => (
                <Card key={index} className="border-border bg-card hover:border-primary/20 transition-all duration-300">
                  <CardContent className="p-8 space-y-4 text-left">
                    <h3 className="text-2xl font-semibold">{study.title}</h3>
                    <p className="text-muted-foreground leading-relaxed">{study.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
        </main>
        <CTAStrip />
        <Footer />
      </div>
    </div>
  );
};

export default Agency;